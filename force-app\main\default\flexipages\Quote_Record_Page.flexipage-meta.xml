<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Quote.Edit_Lines</value>
                            <visibilityRule>
                                <booleanFilter>1 OR 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Draft</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Reject</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Quote.PDF_VIEW</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Approved</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Submit</value>
                            <visibilityRule>
                                <booleanFilter>1 OR 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Draft</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Reject</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Quote.quoteCustomerRefuseAction</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Presented</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Edit</value>
                            <visibilityRule>
                                <booleanFilter>1 OR 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Draft</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Reject</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Quote.ConvertToContract</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Accepted</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Is_Contract__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>false</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>EmailQuote</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Presented</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>CloneMediaPlan</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>hideUpdateButton</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>variant</name>
                    <value>linear</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_pathassistant:pathAssistant</componentName>
                <identifier>runtime_sales_pathassistant_pathAssistant</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>subheader</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AccountId</fieldItem>
                <identifier>RecordAccountIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OpportunityId</fieldItem>
                <identifier>RecordOpportunityIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Status</fieldItem>
                <identifier>RecordStatusField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.StartDate__c</fieldItem>
                <identifier>RecordStartDate_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.StartDate__c</fieldItem>
                <identifier>RecordStartDate_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EndDate__c</fieldItem>
                <identifier>RecordEndDate_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EndDate__c</fieldItem>
                <identifier>RecordEndDate_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TaxCode__c</fieldItem>
                <identifier>RecordTaxCode__cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TaxCode__c</fieldItem>
                <identifier>RecordTaxCode_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExchangeRateIdentification__c</fieldItem>
                <identifier>RecordExchangeRateIdentification_cField</identifier>
                <visibilityRule>
                    <booleanFilter>(1 OR 2 ) and 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.isCurrenySame__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExchangeRateIdentification__c</fieldItem>
                <identifier>RecordExchangeRateIdentification_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2 AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.isCurrenySame__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Non_Standard_Rate__c</fieldItem>
                <identifier>RecordNon_Standard_Rate_cField</identifier>
                <visibilityRule>
                    <booleanFilter>(1 OR 2 ) AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ExchangeRateIdentification__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>0</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Non_Standard_Rate__c</fieldItem>
                <identifier>RecordNon_Standard_Rate_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2 AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ExchangeRateIdentification__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>0</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Potential_Cost_Reminder__c</fieldItem>
                <identifier>RecordPotential_Cost_Reminder_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Potential_Cost_Reminder__c</fieldItem>
                <identifier>RecordPotential_Cost_Reminder_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.sof_Descript__c</fieldItem>
                <identifier>Recordsof_Descript_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.sof_Descript__c</fieldItem>
                <identifier>Recordsof_Descript_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-3da08496-9c49-428f-b411-03a7b309c45b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.QuoteNumber</fieldItem>
                <identifier>RecordQuoteNumberField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Add_Type__c</fieldItem>
                <identifier>RecordAdd_Type_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Add_Type__c</fieldItem>
                <identifier>RecordAdd_Type_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.previous_contracts__c</fieldItem>
                <identifier>Recordprevious_contracts_cField</identifier>
                <visibilityRule>
                    <booleanFilter>(1 OR 2 ) and 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Add_Type__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>新增合同</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.previous_contracts__c</fieldItem>
                <identifier>Recordprevious_contracts_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2 AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Add_Type__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>新增合同</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Product_Cate__c</fieldItem>
                <identifier>RecordProduct_Cate_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Product_Cate__c</fieldItem>
                <identifier>RecordProduct_Cate_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Contract_Cur__c</fieldItem>
                <identifier>RecordContract_Cur__cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Contract_Cur__c</fieldItem>
                <identifier>RecordContract_Cur_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Settlement_Cur__c</fieldItem>
                <identifier>RecordSettlement_Cur__cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Settlement_Cur__c</fieldItem>
                <identifier>RecordSettlement_Cur_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PartyB_Signing_Company__c</fieldItem>
                <identifier>RecordPartyB_Signing_Company_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PartyB_Signing_Company__c</fieldItem>
                <identifier>RecordPartyB_Signing_Company_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Special_Matters__c</fieldItem>
                <identifier>RecordSpecial_Matters_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Special_Matters__c</fieldItem>
                <identifier>RecordSpecial_Matters_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-e29a4672-ab5e-44e5-8fcf-47c55bdb8eed</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-3da08496-9c49-428f-b411-03a7b309c45b</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-e29a4672-ab5e-44e5-8fcf-47c55bdb8eed</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-ea2af36d-fdc6-48e1-8b7f-0bb38260b185</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cucstomer_Contract__c</fieldItem>
                <identifier>RecordCucstomer_Contract_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cucstomer_Contract__c</fieldItem>
                <identifier>RecordCucstomer_Contract_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Customer_Finance_Contact__c</fieldItem>
                <identifier>RecordCustomer_Finance_Contact_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Customer_Finance_Contact__c</fieldItem>
                <identifier>RecordCustomer_Finance_Contact_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-bc1d35c8-b110-4d8f-a2e4-1f8fbc765b8c</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Customer_Tech_Contact__c</fieldItem>
                <identifier>RecordCustomer_Tech_Contact_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Customer_Tech_Contact__c</fieldItem>
                <identifier>RecordCustomer_Tech_Contact_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-673868c5-bd08-487c-b223-66aa63d3f160</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-bc1d35c8-b110-4d8f-a2e4-1f8fbc765b8c</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-673868c5-bd08-487c-b223-66aa63d3f160</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f870b7e3-d56d-45fa-af20-13be3b168f3b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Expense_Settlement_Method__c</fieldItem>
                <identifier>RecordExpense_Settlement_Method_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Expense_Settlement_Method__c</fieldItem>
                <identifier>RecordExpense_Settlement_Method_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payment_Terms__c</fieldItem>
                <identifier>RecordPayment_Terms__cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payment_Terms__c</fieldItem>
                <identifier>RecordPayment_Terms_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.prepayment__c</fieldItem>
                <identifier>Recordprepayment_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 And (2 OR 3)</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>​​AdvancePay</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.prepayment__c</fieldItem>
                <identifier>Recordprepayment_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2 AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>​​AdvancePay</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payment_Way__c</fieldItem>
                <identifier>RecordPayment_Way_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND ( 2 OR 3)</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>​​Installment​</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payment_Way__c</fieldItem>
                <identifier>RecordPayment_Way_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2 AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>​​Installment​</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-5d69fcdd-0176-4a51-a16a-09446f5b9f75</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payment_Cycle__c</fieldItem>
                <identifier>RecordPayment_Cycle_cField</identifier>
                <visibilityRule>
                    <booleanFilter>(1 OR 2) AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>​​Installment​</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payment_Cycle__c</fieldItem>
                <identifier>RecordPayment_Cycle_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2 AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>​​Installment​</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DepositAmount__c</fieldItem>
                <identifier>RecordDepositAmount_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 and (2 OR 3)</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Deposit​​AndPostPay</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DepositAmount__c</fieldItem>
                <identifier>RecordDepositAmount_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2 AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Deposit​​AndPostPay</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Total_Installment_Amount__c</fieldItem>
                <identifier>RecordTotal_Installment_Amount_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 and (2 OR 3)</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>​​Installment​</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Total_Installment_Amount__c</fieldItem>
                <identifier>RecordTotal_Installment_Amount_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2 AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>​​Installment​</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Number_of_Installments__c</fieldItem>
                <identifier>RecordNumber_of_Installments_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 and (2 OR 3)</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>​​Installment​</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Number_of_Installments__c</fieldItem>
                <identifier>RecordNumber_of_Installments_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2 AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>​​Installment​</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payment_Settlement_Method_Description__c</fieldItem>
                <identifier>RecordPayment_Settlement_Method_Description_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payment_Settlement_Method_Description__c</fieldItem>
                <identifier>RecordPayment_Settlement_Method_Description_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Reject</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApprovalStatus__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Draft</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-24f2c1a3-3be9-4d59-9eff-7881971d3d65</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-5d69fcdd-0176-4a51-a16a-09446f5b9f75</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-24f2c1a3-3be9-4d59-9eff-7881971d3d65</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column6</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-e12aed78-24c8-4ec9-b6e9-d5155da6765b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CreatedById</fieldItem>
                <identifier>RecordCreatedByIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-c810c69a-e960-494b-92ec-0f50525abcaa</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LastModifiedById</fieldItem>
                <identifier>RecordLastModifiedByIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-41025173-63b6-4f45-afc7-44f4e739631a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c810c69a-e960-494b-92ec-0f50525abcaa</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-41025173-63b6-4f45-afc7-44f4e739631a</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column10</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-75571b8f-14e2-43a8-970b-71f44e93c799</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-ea2af36d-fdc6-48e1-8b7f-0bb38260b185</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>@@@SFDCQuote_InformationSFDC@@@</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-f870b7e3-d56d-45fa-af20-13be3b168f3b</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>负责人信息</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-e12aed78-24c8-4ec9-b6e9-d5155da6765b</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>结算信息</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-75571b8f-14e2-43a8-970b-71f44e93c799</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>@@@SFDCSystem_InformationSFDC@@@</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection5</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-6bd9bb28-20f7-4e9a-a646-4284c08c61b7</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>startInEditMode</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>quoteLineGroupCreate</componentName>
                <identifier>c_quoteLineGroupCreate</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-023a02ac-93c2-4cfb-8897-c963673b4857</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-6bd9bb28-20f7-4e9a-a646-4284c08c61b7</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-023a02ac-93c2-4cfb-8897-c963673b4857</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>报价行</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-fbd5baed-1017-4434-9684-1e6f6b6a73d5</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-fbd5baed-1017-4434-9684-1e6f6b6a73d5</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>MassChangeOwner</value>
                        </valueListItems>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Quote.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Installments_Item__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>InstallNo__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Amount__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>StartDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>EndDate__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>分期行项目</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Expense_Settlement_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>​​Installment​</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Quote.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Contracts__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Quote.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>ProcessSteps</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Quote.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>CombinedAttachments</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer3</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Quote Record Page</masterLabel>
    <parentFlexiPage>runtime_sales_quotes__Quote_rec_L</parentFlexiPage>
    <sobjectType>Quote</sobjectType>
    <template>
        <name>flexipage:recordHomeWithSubheaderTemplateDesktop</name>
    </template>
    <type>RecordPage</type>
</FlexiPage>
